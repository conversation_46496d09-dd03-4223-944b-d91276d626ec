kind: ConfigMap
apiVersion: v1
metadata:
  name: aws-logging
  namespace: aws-observability
data:
  parsers.conf: |
    [PARSER]
        Name crio
        Format Regex
        Regex ^(?<time>[^ ]+) (?<stream>stdout|stderr) (?<logtag>P|F) (?<log>.*)$
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z
        Decode_Field_As json log

  filters.conf: |
    [FILTER]
        Name parser
        Match *
        Key_name log
        Parser crio

    [FILTER]
        Name kubernetes
        Match kube.*
        Merge_Log On
        Buffer_Size 0
        Kube_Meta_Cache_TTL 300s

  output.conf: |
    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_core*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/core
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match_Regex .*dcbg-core_bcclient(?!.*(\-stream)).*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/bcclient
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_bcclient-stream*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/bcclient-stream
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_bcmonitoring*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/bcmonitoring
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_bctracker-balance*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/bctracker-balance
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_bctracker-transaction*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/bctracker-transaction
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_newrelic-infra-agent*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/newrelic-infra-agent
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_prometheus*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/prometheus
        log_stream_prefix from-fluent-bit-
        auto_create_group false

    [OUTPUT]
        Name cloudwatch_logs
        Match *dcbg-core_invoke-core-tracker*
        region {{ .Values.region }}
        log_group_name /aws/eks/{{ .Values.envName }}/fargate/invoke-core-tracker
        log_stream_prefix from-fluent-bit-
        auto_create_group false
