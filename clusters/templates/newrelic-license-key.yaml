apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: {{ .Values.esoServiceRoleArn }}
  name: external-secrets
  namespace: dcbg-core
---
apiVersion: external-secrets.io/v1
kind: SecretStore
metadata:
  name: newrelic-license-key
  namespace: dcbg-core
spec:
  provider:
    aws:
      service: ParameterStore
      region: {{ .Values.region }}
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets
---
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: newrelic-license-key
  namespace: dcbg-core
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: newrelic-license-key
    kind: SecretStore
  target:
    creationPolicy: Owner
  data:
    - secretKey: license-key
      remoteRef:
        key: /newrelic/license_key # parameter store path
