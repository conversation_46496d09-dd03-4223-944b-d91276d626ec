apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: newrelic-infrastructure
  name: newrelic-infra-agent
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - nodes/metrics
      - nodes/stats
      - nodes/proxy
      - pods
      - services
    verbs:
      - get
      - list
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-core-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: core-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-bcclient-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: bcclient-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-bcclient-stream-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: bcclient-stream-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-bcmonitoring-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: bcmonitoring-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-bctracker-balance-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: bctracker-balance-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-bctracker-transaction-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: bctracker-transaction-service-account
    namespace: dcbg-core
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-prometheus-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: prometheus-service-account
    namespace: dcbg-core
