---
minReplicas: 1
maxReplicas: 2
newrelic-prometheus-agent:
  cluster: "{{ .Values.clusterName }}"
  customSecretName: newrelic-license-key
  customSecretLicenseKey: license-key
  images:
    prometheus:
      tag: v2.45.6
  serviceAccount:
    create: false
    name: "prometheus-service-account"
  labels:
    app: prometheus
  podLabels:
    app: prometheus
  config:
    extra_scrape_configs:
      - job_name: besu
        scrape_interval: 15s
        scrape_timeout: 10s
        metrics_path: /metrics
        scheme: http
        ec2_sd_configs:
          # extra_scrape_configs 内ではパラメータを展開できないため Tokyo / Osaka の両方を指定する
          # https://github.com/newrelic/newrelic-prometheus-configurator/blob/newrelic-prometheus-agent-1.11.0/charts/newrelic-prometheus-agent/values.yaml#L468-L473
          - region: ap-northeast-1
            port: 9545
          - region: ap-northeast-3
            port: 9545
        relabel_configs:
          - source_labels: [__meta_ec2_tag_Kind]
            regex: besu-(listener|validator)
            action: keep
          - source_labels: [__meta_ec2_tag_Name]
            target_label: name
          - source_labels: [__meta_ec2_tag_Environment]
            target_label: environment
          - source_labels: [__meta_ec2_region]
            target_label: region
newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.4.0
  resources:
    requests:
      cpu: "200m"
      memory: "100M"
    limits:
      cpu: "200m"
      memory: "100M"
serviceAccount:
  name: "prometheus-service-account"
