apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: newrelic-infrastructure
  name: newrelic-infra-agent
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - nodes/metrics
      - nodes/stats
      - nodes/proxy
      - pods
      - services
    verbs:
      - get
      - list
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-relayer-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: relayer-service-account
    namespace: dcbg-ibc
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: newrelic-infra-agent-prometheus-service-account
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: newrelic-infra-agent
subjects:
  - kind: ServiceAccount
    name: prometheus-service-account
    namespace: dcbg-ibc
