# dcbg-dcf-kubernetes-sandbox

## はじめに

リポジトリを clone したら、pre-commit を設定するために、下記のコマンドを実行してください。<br>
`git config --local core.hooksPath .githooks`

## tools

AWS Load Balancer Controller をインストール/アップデートするためのスクリプト。[使用方法は Confluence を参照](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2532967600/AWS+Load+Balancer+Controller+ALBC)

- ./tools/install_aws_load_balancer_controller.sh
- ./tools/update_aws_load_balancer_controller.sh

External Secrets Operator をインストール/アップデートするためのスクリプト。[使用方法は Confluence を参照](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2770862081/External+Secrets+Operator+ESO)

- ./tools/install_external_secrets_operator.sh
- ./tools/update_external_secrets_operator.sh

## helm コマンド実行

helm コマンドを使用するため、事前にインストールしておくこと。

https://helm.sh/ja/docs/intro/install/

### 適用されるマニフェストを確認する

template コマンドで変数などもレンダリングされたマニフェストを標準出力に出すことができる。これを用いてどのようなマニフェストになるかを確認する。
以下は、dev-fin に適用するマニフェストを出力する場合のコマンド例。

```
helm template --values dev/values-dev-fin.yaml ./
```

### helm chart をデプロイする

install コマンドで chart をインストールする。
以下は dev-fin に"core"というリリース名でデプロイする場合のコマンド例。

```
helm install core --values dev/values-dev-fin.yaml ./
```

### クラスタ上の状態との diff を確認する

diff コマンドでクラスタ上にすでにデプロイされたものとの diff を取ることができる。

diff コマンドは plugin のため事前にインストールしておく必要がある。以下はインストールのためのコマンド。(helm2.3 以上のバージョンが必要)

```
helm plugin install https://github.com/databus23/helm-diff
```

以下は dev-fin に"core"というリリースがあった場合のコマンド例。

```
helm diff upgrade --values dev/values-dev-fin.yaml core ./
```

## Workflow の説明

### Dev 環境

- [Dev] restart pods
  - dev 環境の bcclient,bcmonitoring,tracker,shared の pod を再起動する workflow
- [Dev] restart bcclient pods
- [Dev] restart bcmonitoring pods
- [Dev] restart bcmonitoring_tracker pods
- [Dev] restart bcmonitoring_shared pods
  - dev 環境の各 pod を再起動する workflow

### Sandbox-fin 環境

- [Sandbox] restart pods
  - Sandbox-fin 環境の bcclient,bcmonitoring,tracker,shared の pod を再起動する workflow
- [Sandbox] restart bcclient pods
- [Sandbox] restart bcmonitoring pods
- [Sandbox] restart bcmonitoring_tracker pods
- [Sandbox] restart bcmonitoring_shared pods
  - Sandbox-fin 環境の各 pod を再起動する workflow

### Sandbox-ind 環境

- [SandboxExt] restart pods
  - Sandbox-ind 環境の bcclient,bcmonitoring,tracker の pod を再起動する workflow
- [SandboxExt] restart bcclient pods
- [SandboxExt] restart bcmonitoring pods
- [SandboxExt] restart bcmonitoring_tracker pods
  - Sandbox-ind 環境の各 pod を再起動する workflow

### Sandbox-int 環境

- [SandboxInt] restart relayer pods
  - Sandbox-int 環境の各 pod を再起動する workflow
