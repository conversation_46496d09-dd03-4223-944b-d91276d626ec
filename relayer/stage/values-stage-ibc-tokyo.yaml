envName: stage-ibc

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-ibc-relayer
    tag: "4cb2aba"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-ibc-tokyo-eks-pod-core

isMultitenancy: false

configMap:
  # ZONE_ID: 3001
  3001:
    RLY_BIN: "/bin/rly"
    RLY_CONFIG_HOME: "/opt/relayer/.relayer"
    CHAIN_1_RPC_ADDRESS: "http://stage-ibc-tokyo-besu-fin-lb-13f69ee20ea890c8.elb.ap-northeast-1.amazonaws.com:8451"
    CHAIN_2_RPC_ADDRESS: "http://stage-ibc-tokyo-besu-ind-lb-da230bbceda553f8.elb.ap-northeast-1.amazonaws.com:8451"
    CHAIN_1_ETH_CHAIN_ID: "1234"
    CHAIN_2_ETH_CHAIN_ID: "5678"
    CHAIN_1_IBC_ADDRESS: "******************************************"
    CHAIN_2_IBC_ADDRESS: "******************************************"
    CHAIN_1_CHAIN_ID: "********"
    CHAIN_2_CHAIN_ID: "********"

    # Fin Zone Path
    CHAIN_1_AS_CLIENT_ID: "hb-ibft2-3"
    CHAIN_1_BS_CLIENT_ID: "hb-ibft2-4"
    CHAIN_1_TT_CLIENT_ID: "hb-ibft2-5"
    CHAIN_1_AS_CONNECTION_ID: "connection-3"
    CHAIN_1_BS_CONNECTION_ID: "connection-4"
    CHAIN_1_TT_CONNECTION_ID: "connection-5"
    CHAIN_1_AS_CHANNEL_ID: "channel-3"
    CHAIN_1_BS_CHANNEL_ID: "channel-4"
    CHAIN_1_TT_CHANNEL_ID: "channel-5"

    # Biz Zone Path
    CHAIN_2_AS_CLIENT_ID: "hb-ibft2-3"
    CHAIN_2_BS_CLIENT_ID: "hb-ibft2-4"
    CHAIN_2_TT_CLIENT_ID: "hb-ibft2-5"
    CHAIN_2_AS_CONNECTION_ID: "connection-3"
    CHAIN_2_BS_CONNECTION_ID: "connection-4"
    CHAIN_2_TT_CONNECTION_ID: "connection-5"
    CHAIN_2_AS_CHANNEL_ID: "channel-3"
    CHAIN_2_BS_CHANNEL_ID: "channel-4"
    CHAIN_2_TT_CHANNEL_ID: "channel-5"




