envName: stage-ibc-b

# minReplicas: 3 最小構成のためデフォルトを使用
# maxReplicas: 5 最小構成のためデフォルトを使用

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-ibc-b-relayer
    tag: "4cb2aba"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-ibc-tokyo-eks-pod-core

configMap:
  RLY_BIN: "/bin/rly"
  RLY_CONFIG_HOME: "/opt/relayer/.relayer"
  CHAIN_1_RPC_ADDRESS: "http://stage-ibc-b-tokyo-besu-fin-lb-29bc6d200b06c2fc.elb.ap-northeast-1.amazonaws.com:8451"
  CHAIN_1_ETH_CHAIN_ID: "1234"
  CHAIN_2_RPC_ADDRESS: "http://stage-ibc-b-tokyo-besu-ind-lb-9c12903310cf4771.elb.ap-northeast-1.amazonaws.com:8451"
  CHAIN_2_ETH_CHAIN_ID: "5679"
  CHAIN_1_IBC_ADDRESS: "******************************************"
  CHAIN_2_IBC_ADDRESS: "******************************************"
