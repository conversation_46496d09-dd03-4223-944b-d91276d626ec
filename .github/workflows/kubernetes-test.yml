name: "kubernetes-test"

on:
  workflow_dispatch:
  repository_dispatch:
    types: [test-workflow]

jobs:
  deploy_dev:

    runs-on: ubuntu-latest

    steps:
      - if: github.event.client_payload.echo_string
        name: echo string
        run: echo ${{ toJSON(github.event.client_payload.echo_string) }}
        
      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: v1.22.0

      - uses: actions/checkout@v3

      - name: Configure AWS Credentials for Dev
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN_DEV }}
          role-duration-seconds: 1800

      - name: dev test
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA }}
        run: |
          echo "$KUBE_CONFIG_DATA" | base64 --decode > /tmp/kube_config
          export KUBECONFIG=/tmp/kube_config
          kubectl get pods -n dcfc-core

      - name: Configure AWS Credentials for sandbox-fin
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN_SANDBOX_FIN }}
          role-duration-seconds: 1800

      - name: sandbox-fin test
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA_SANDBOX }}
        run: |
          echo "$KUBE_CONFIG_DATA" | base64 --decode > /tmp/kube_config
          export KUBECONFIG=/tmp/kube_config
          kubectl get pods -n dcfc-core

      - name: Configure AWS Credentials for sandbox-ind
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN_SANDBOX_IND }}
          role-duration-seconds: 1800

      - name: sandbox-ind test
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA_SANDBOX_IND }}
        run: |
          echo "$KUBE_CONFIG_DATA" | base64 --decode > /tmp/kube_config
          export KUBECONFIG=/tmp/kube_config
          kubectl get pods -n dcfc-core
