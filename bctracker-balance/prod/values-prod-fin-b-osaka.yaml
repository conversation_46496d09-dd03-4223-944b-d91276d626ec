envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/prod-fin-b-bctracker-balance
    tag: "676809c"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-osaka-eks-pod-bctracker-balance

configMap:
  DB_BASE: "prod-fin-b-osaka-rds-cluster.cluster-c9cy6qm04iby.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  LOCAL_STACK_ENDPOINT: ""
  AWS_DYNAMODB_REGION: "ap-northeast-3"
