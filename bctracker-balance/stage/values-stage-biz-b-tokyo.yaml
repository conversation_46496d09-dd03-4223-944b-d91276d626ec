envName: stage-biz-b

# minReplicas: 3 最小構成のためデフォルトを使用
# maxReplicas: 5 最小構成のためデフォルトを使用

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-biz-b-bctracker-balance
    tag: "aaaaaa"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-biz-b-tokyo-eks-pod-bctracker-balance

configMap:
  DB_BASE: "stage-biz-b-tokyo-rds-cluster.cluster-cpsce66csmz0.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  LOCAL_STACK_ENDPOINT: ""
  AWS_DYNAMODB_REGION: "ap-northeast-1"