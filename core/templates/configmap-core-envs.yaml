apiVersion: v1
kind: ConfigMap
metadata:
  name: core-envs
  namespace: dcbg-core
data:
# baseにある環境変数はハードコード
  DB_MINIMUM_IDLE: "10"
  DB_CONNECTION_TIMEOUT: "30000"
  DB_IDLE_TIMEOUT: "600000"
  BC_CLIENT_PROTOCOL: "http"
  BC_CLIENT_BASE: "bcclient.dcbg-core.svc.cluster.local"
  HTTP_CONNECTION_MAX_PER_ROUTE: "50"
  HTTP_CONNECTION_MAX_TOTAL: "50"
  DEFAULT_LIMIT: "100"
  AWS_ACCESS_TOKEN_VALIDITY: "24"
  REFRESH_TOKEN_VALIDITY: "30"
  COGNITO_LOCAL_ENDPOINT: ""
  LOG_OUTPUT_MAX_LENGTH_FROM_ITEM: "4096"
  PERIOD: "999999999999999999"
  DEADLINE: "7200"
  TIMEOUT_TIMESTAMP: "60"
  BC_CLIENT_READ_TIMEOUT_MILLISEC: "20000"
  TRANSFER_LIMIT: "999999999999"
  CHARGE_LIMIT: "999999999999"
  DISCHARGE_LIMIT: "999999999999"
  MINT_LIMIT: "999999999999"
  BURN_LIMIT: "999999999999"
  CUMULATIVE_LIMIT: "999999999999999"
  CUMULATIVE_MINT_LIMIT: "999999999999999"
  CUMULATIVE_BURN_LIMIT: "999999999999999"
  CUMULATIVE_TRANSFER_LIMIT: "999999999999999"
  CUMULATIVE_CHARGE_LIMIT: "999999999999999"
  CUMULATIVE_DISCHARGE_LIMIT: "999999999999999"
  DYNAMODB_BALANCE_CACHE_TABLE: "balance_cache"
  LOCALSTACK_ENDPOINT: ""
  SERVER_TOMCAT_THREADS_MAX: "400"
# 環境ごとに変更する変数はvaluesのconfigmapに定義されているところからループで取得
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
