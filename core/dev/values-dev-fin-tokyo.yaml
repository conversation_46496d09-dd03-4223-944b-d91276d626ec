envName: dev-fin

minReplicas: 1
maxReplicas: 1

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/dev-fin-core
    tag: "74ac4ba"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/dev-fin-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/dev-fin-tokyo-core-alb-tg/6f279a638bc3a9e9

configMap:
  DB_BASE: "dev-fin-tokyo-rds-cluster.cluster-c3swqgm1n2fm.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_RRtBlTIvg/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_RRtBlTIvg"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"
  HTTP_CONNECTION_MAX_PER_ROUTE: "400"
  HTTP_CONNECTION_MAX_TOTAL: "400"
