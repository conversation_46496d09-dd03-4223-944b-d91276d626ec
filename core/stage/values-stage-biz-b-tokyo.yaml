envName: stage-biz-b

# minReplicas: 3 最小構成のためデフォルトを使用
# maxReplicas: 5 最小構成のためデフォルトを使用

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-biz-b-core
    tag: "aaaaaa"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-biz-b-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/stage-biz-b-tokyo-core-alb-tg/480a1f99456d9c08

configMap:
  DB_BASE: "stage-biz-b-tokyo-rds-cluster.cluster-cpsce66csmz0.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_U2ELVfwnc/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_U2ELVfwnc"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "business_zone"