envName: stage-biz-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-biz-a-core
    tag: "9f81c4e"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-biz-a-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/stage-biz-a-tokyo-core-alb-tg/89bc9a1d0a213b23

configMap:
  DB_BASE: "stage-biz-a-tokyo-rds-cluster.cluster-c96qwwwq80k9.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_gpvtaoUwl/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_gpvtaoUwl"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "business_zone"