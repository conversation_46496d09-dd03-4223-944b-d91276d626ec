envName: stage-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/stage-fin-b-core
    tag: "aaaaaa"

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-b-osaka-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-3:************:targetgroup/stage-fin-b-osaka-core-alb-tg/a2607ea536b78293

configMap:
  DB_BASE: "stage-fin-b-osaka-rds-cluster.cluster-cjaq6k8okk6j.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_hbd6PwBiO/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_hbd6PwBiO"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-3"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""