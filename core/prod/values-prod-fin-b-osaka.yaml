envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/prod-fin-b-core
    tag: "710bb64"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-osaka-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-3:************:targetgroup/prod-fin-b-osaka-core-alb-tg/3d2914c3c61f0c8a

configMap:
  DB_BASE: "prod-fin-b-osaka-rds-cluster.cluster-c9cy6qm04iby.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "60"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_X7S98iIks/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_X7S98iIks"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-3"
  DYNAMODB_REGION: "ap-northeast-3"
  KMS_REGION: "ap-northeast-3"
  KMS_REPLICA_REGION: ""
  SYSTEMS_MANAGER_REGION: "ap-northeast-3"
  SECRETS_MANAGER_REGION: "ap-northeast-3"
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"