envName: prod-fin-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-fin-a-core
    tag: "774319d"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-a-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/prod-fin-a-tokyo-core-alb-tg/9e8599d2f509f021

configMap:
  DB_BASE: "prod-fin-a-tokyo-rds-cluster.cluster-c5yukcusq5me.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_ttK7uVPoR/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_ttK7uVPoR"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: "ap-northeast-3"
  SECRETS_MANAGER_REPLICA_REGION: "ap-northeast-3"
  ZONE_TYPE: "financial_zone"