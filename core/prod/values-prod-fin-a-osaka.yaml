envName: prod-fin-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/prod-fin-a-core
    tag: "774319d"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-a-osaka-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-3:************:targetgroup/prod-fin-a-osaka-core-alb-tg/55658209e907a115

configMap:
  DB_BASE: "prod-fin-a-osaka-rds-cluster.cluster-cloaqe86sj6v.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "88"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_ttK7uVPoR/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_ttK7uVPoR"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-3"
  KMS_REPLICA_REGION: ""
  SECRETS_MANAGER_REPLICA_REGION: ""
  ZONE_TYPE: "financial_zone"