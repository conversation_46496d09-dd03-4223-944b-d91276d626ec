envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-fin-b-core
    tag: "710bb64"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-tokyo-eks-pod-core

targetGroupBinding:
  targetGroupArn: arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/prod-fin-b-tokyo-core-alb-tg/1e87309b327530aa

configMap:
  DB_BASE: "prod-fin-b-tokyo-rds-cluster.cluster-ctsscu4sabwv.ap-northeast-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  DB_MAXIMUM_POOL_SIZE: "60"
  BC_CLIENT_PORT: "8081"
  AUTH_ISSUER_URL: "https://cognito-idp.ap-northeast-1.amazonaws.com/ap-northeast-1_X7S98iIks/.well-known/jwks.json"
  USER_POOL_ID: "ap-northeast-1_X7S98iIks"
  AWS_COGNITO_REGION: "ap-northeast-1"
  AWS_DYNAMODB_REGION: "ap-northeast-1"
  DYNAMODB_REGION: "ap-northeast-1"
  KMS_REGION: "ap-northeast-1"
  KMS_REPLICA_REGION: "ap-northeast-3"
  SYSTEMS_MANAGER_REGION: "ap-northeast-1"
  SECRETS_MANAGER_REGION: "ap-northeast-1"
  SECRETS_MANAGER_REPLICA_REGION: "ap-northeast-3"
  ZONE_TYPE: "financial_zone"
