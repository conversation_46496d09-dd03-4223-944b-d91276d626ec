apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bcmonitoring-java-hpa
  namespace: dcbg-core
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bcmonitoring-java
  minReplicas: {{ .Values.minReplicas }}
  maxReplicas: {{ .Values.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
