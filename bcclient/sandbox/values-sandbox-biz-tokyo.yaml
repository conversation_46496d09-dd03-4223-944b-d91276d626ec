envName: sbx-biz

application:
  image:
    # bcclient-call-onlyも同じimageを使用する
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/sbx-biz-bcclient
    tag: "ff5b198"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/sbx-biz-tokyo-eks-pod-bcclient

#targetGroupBinding:
#  targetGroupArn: TargetGroupのARN(Terraformで構築後に記載)

configMap:
  WEBSOCKET_URI_HOST: "sbx-biz-tokyo-besu-lb-27b06d27db235319.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "sbx-biz-tokyo-fin-besu-lb-7ff41b681f36f5a0.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "sbx-biz-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "sbx-biz-tokyo-external-abijson"
