apiVersion: v1
kind: ConfigMap
metadata:
  name: bcclient{{ if eq .Release.Name "bcclient-call-only" }}-call-only{{ end }}-envs
  namespace: dcbg-core
data:
  ABI_FORMAT: "hardhat"
  USE_SUB_WEBSOCKET: "true"
  SUBSCRIPTION_CHECK_INTERVAL: "3000"
  REQUEST_TIMEOUT_SEC: "10000"
  GAS_LIMIT: "6721975"
  SENDING_THRESHOLD: "200"
  S3_LOCAL_ENDPOINT: ""
  SQS_QUEUE_NAME: "dcjpy_bcclient_queue_send-transaction.fifo"
  SQS_VISIBILITY_TIMEOUT: "5"
  SQS_WAIT_TIME_SECONDS: "1"
  LOCAL_STACK_ENDPOINT: ""
  TRANSACTION_QUEUE_SECONDARY_INDEX: "transaction_hash_index"
  SERVER_TOMCAT_THREADS_MAX: "400"
{{- range $key, $val := .Values.configMap }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
