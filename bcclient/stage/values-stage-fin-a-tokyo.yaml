envName: stage-fin-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    # bcclient-call-onlyも同じimageを使用する
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-fin-a-bcclient
    tag: "07a45e9"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-a-tokyo-eks-pod-bcclient

#targetGroupBinding:
#  targetGroupArn: TargetGroupのARN(Terraformで構築後に記載)

configMap:
  WEBSOCKET_URI_HOST: "stage-fin-a-tokyo-besu-lb-531f3340229d8c3f.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  WEBSOCKET_LISTENER_URI_HOST: "stage-fin-a-tokyo-besu-lb-531f3340229d8c3f.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_LISTENER_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "stage-fin-a-tokyo-besu-lb-531f3340229d8c3f.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_LISTENER_URI_HOST: "stage-fin-a-tokyo-besu-lb-531f3340229d8c3f.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_LISTENER_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "stage-fin-a-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "stage-fin-a-tokyo-abijson"
