minReplicas: 1
maxReplicas: 2

application:
  image:
    pullPolicy: IfNotPresent
  ports:
    containerPort: 8081
  resources:
    requests:
      cpu: "1"
      memory: "2Gi"
    limits:
      cpu: "1"
      memory: "2Gi"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.4.0
  resources:
    requests:
      cpu: "200m"
      memory: "100M"
    limits:
      cpu: "200m"
      memory: "100M"

serviceAccount:
  name: "bcclient-service-account"

targetGroupBinding:
  targetGroupArn:
