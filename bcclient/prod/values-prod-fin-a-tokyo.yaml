envName: prod-fin-a

minReplicas: 3
maxReplicas: 5

application:
  image:
    # bcclient-call-onlyも同じimageを使用する
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-fin-a-bcclient
    tag: "ff5b198"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-a-tokyo-eks-pod-bcclient

#targetGroupBinding:
#  targetGroupArn: TargetGroupのARN(Terraformで構築後に記載)

configMap:
  WEBSOCKET_URI_HOST: "prod-fin-a-tokyo-besu-lb-7c398ea9d73fdee8.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "prod-fin-a-tokyo-besu-lb-7c398ea9d73fdee8.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "prod-fin-a-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "prod-fin-a-tokyo-abijson"
