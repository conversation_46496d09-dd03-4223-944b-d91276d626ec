envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/prod-fin-b-bcclient
    tag: "7d97151"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-tokyo-eks-pod-bcclient

configMap:
  WEBSOCKET_URI_HOST: "prod-fin-b-tokyo-besu-lb-8527aae60c2e7d82.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "prod-fin-b-tokyo-besu-lb-8527aae60c2e7d82.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "prod-fin-b-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "prod-fin-b-tokyo-external-abijson"

targetGroupBinding:
  targetGroupArn: "arn:aws:elasticloadbalancing:ap-northeast-1:************:targetgroup/prod-fin-b-tokyo-tg-bcc-int/aac9c843e5036bfd"
