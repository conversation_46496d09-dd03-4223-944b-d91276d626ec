envName: prod-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/prod-fin-b-bcclient
    tag: "7d97151"

serviceAccount:
  roleArn: arn:aws:iam::************:role/prod-fin-b-osaka-eks-pod-bcclient

configMap:
  WEBSOCKET_URI_HOST: "prod-fin-b-osaka-besu-lb-9342de758a5f57c5.elb.ap-northeast-3.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "prod-fin-b-osaka-besu-lb-9342de758a5f57c5.elb.ap-northeast-3.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "prod-fin-b-osaka-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "prod-fin-b-osaka-external-abijson"

targetGroupBinding:
  targetGroupArn: "arn:aws:elasticloadbalancing:ap-northeast-3:************:targetgroup/prod-fin-b-osaka-tg-bcc-int/e4bbf9d218280d4e"