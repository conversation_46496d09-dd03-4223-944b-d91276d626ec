envName: dev-biz

application:
  image:
    # bcclient-call-onlyも同じimageを使用する
    name: ************.dkr.ecr.ap-northeast-1.amazonaws.com/dev-biz-bcclient
    tag: "ff5b198"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: arn:aws:iam::************:role/dev-biz-tokyo-eks-pod-bcclient

#targetGroupBinding:
#  targetGroupArn: TargetGroupのARN(Terraformで構築後に記載)

configMap:
  WEBSOCKET_URI_HOST: "dev-biz-tokyo-besu-lb-42d6fffab5420d07.elb.ap-northeast-1.amazonaws.com"
  WEBSOCKET_URI_PORT: "8541"
  SUB_WEBSOCKET_URI_HOST: "dev-biz-tokyo-fin-besu-lb-2507d369b8c495ac.elb.ap-northeast-1.amazonaws.com"
  SUB_WEBSOCKET_URI_PORT: "8541"
  CONTRACT_BUCKET_NAME: "dev-biz-tokyo-abijson"
  EXTERNAL_CONTRACT_BUCKET_NAME: "dev-biz-tokyo-external-abijson"
