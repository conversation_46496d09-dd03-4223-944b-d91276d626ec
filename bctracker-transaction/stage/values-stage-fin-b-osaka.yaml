envName: stage-fin-b

minReplicas: 3
maxReplicas: 5

application:
  image:
    name: ************.dkr.ecr.ap-northeast-3.amazonaws.com/stage-fin-b-bctracker-transaction
    tag: "5cd2193"

serviceAccount:
  roleArn: arn:aws:iam::************:role/stage-fin-b-osaka-eks-pod-bctracker-transaction

configMap:
  DB_BASE: "stage-fin-b-osaka-rds-cluster.cluster-cjaq6k8okk6j.ap-northeast-3.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "core_db"
  LOCAL_STACK_ENDPOINT: ""
  ZONE_TYPE: "financial_zone"