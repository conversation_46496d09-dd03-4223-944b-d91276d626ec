check_type: yaml
env_define_map:
  CHAIN_1_RPC_ADDRESS:
    doc_define_name:
      - "NLB-Besu-fin-URL"
      - "Besu-Listener-fin-Port-HTTP-JSON-RPC"
    doc_action: "join"
    doc_format: "{0[0]}:{0[1]}"
  CHAIN_1_ETH_CHAIN_ID:
    doc_define_name:
      - "Besu-Listener-fin-genesis.json"
    doc_action: "parse"
    doc_format: "json"
  CHAIN_2_RPC_ADDRESS:
    doc_define_name:
      - "NLB-Besu-biz-URL"
      - "Besu-Listener-biz-Port-HTTP-JSON-RPC"
    doc_action: "join"
    doc_format: "{0[0]}:{0[1]}"
  CHAIN_2_ETH_CHAIN_ID:
    doc_define_name:
      - "Besu-Listener-biz-genesis.json"
    doc_action: "parse"
    doc_format: "json"