import os
import errno
import argparse
import yaml
import ujson
import pandas as pd
import re


def get_target_config(component, zone):
    """Return target configuration file name from base configuration and target configuration file.

    """
    try:
        config_base = os.path.join(os.environ['SCRIPT_BASE'], "config")
        if "bcclient" == component or "bcclient-stream" == component:
            config_file = "{0}-{1}.yaml".format(os.path.join(config_base, component), zone)
        else:
            config_file = "{0}.yaml".format(os.path.join(config_base, component))
        if not os.path.isfile(config_file):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), config_file)

        with open(config_file, encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
        return yaml_config

    except Exception as e:
        raise


def get_aws_resource_raw_data(aws_file_name, target_yaml):
    """Get items that match the YAML definition from the AWS resource raw data.

    """
    try:
        aws_doc_base = "."
        aws_file = os.path.join(aws_doc_base, aws_file_name)
        if not os.path.isfile(aws_file):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), aws_file)

        aws_resources_df = pd.read_csv(aws_file, names=['module', 'column', 'value', 'note'])
        match_dict = {}

        if not target_yaml["env_define_map"]:
            print("**************************************************************")
            print("No comparison data available for the AWS resource raw data.")
            print("**************************************************************")
            return match_dict

        for key, values in target_yaml["env_define_map"].items():
            match_values = []

            doc_define_names = values.get("doc_define_name")
            doc_action = values.get("doc_action")
            doc_format = values.get("doc_format")

            for doc_define_name in doc_define_names:

                match_value = \
                    (aws_resources_df[aws_resources_df['column'] == doc_define_name]).to_dict(orient='records')[0][
                        'value']

                if match_value is None:
                    raise

                if ".json" not in doc_define_name:
                    if doc_action is None or 'trim' not in doc_action:
                        match_values.append(match_value)
                    else:
                        match_values.append(re.sub(str(doc_format), "", match_value))
                else:
                    json_obj = ujson.loads(match_value)
                    match_values.append(json_obj['config']['chainId'])

            match_dict[key] = str(match_values[0])

            if "join" == doc_action:
                match_dict[key] = doc_format.format(match_values)

        return match_dict

    except Exception as e:
        raise


def diff_k8s_env_values(k8s_values_yaml, aws_resource_data):

    if not aws_resource_data.items():
        print("No comparison data available for the AWS resource raw data.")

    k8s_config_values = None

    for idx, k8s_doc in enumerate(k8s_values_yaml, 1):
        if k8s_doc is not None and 'ConfigMap' in k8s_doc['kind']:
            k8s_config_values = sorted(k8s_doc['data'].items())
        else:
            continue

        print("---------------------------------------------------------------")
        print("Target k8s Config Map Name\t: {0}".format(k8s_doc['metadata']['name']))

        for aws_key, aws_value in sorted(aws_resource_data.items()):
            for k8s_config_value in k8s_config_values:
                k8s_key = k8s_config_value[0]
                k8s_value = k8s_config_value[1]

                if aws_key == k8s_key:
                    is_match = (k8s_value == aws_value)
                    print("{0}\t: Result -> {1}".format(k8s_key, "MATCH" if is_match else "UNMATCH"))
                    print("\tK8S_ENV_VALUE\t: {0}".format(k8s_value))
                    print("\tAWS_VALUE\t: {0}".format(aws_value))


def diff_pod_env_values(pod_envs_file, aws_resource_data):
    k8s_config_values = None

    if not os.path.isfile(pod_envs_file):
        raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), aws_file)

    with open(pod_envs_file, 'r', encoding='utf-8') as file:
        pod_envs = " ".join(file.readlines()).split()

    pod_name = pod_envs_file.split("/")[-1].split(".")[0]

    for aws_key, aws_value in sorted(aws_resource_data.items()):
        for pod_env in pod_envs:
            if "=" not in pod_env:
                continue
            pod_env_key, pod_env_value = pod_env.strip().split("=", 1)

            if aws_key == pod_env_key:
                is_match = (pod_env_value == aws_value)
                print("{0}\t: Result -> {1}".format(pod_env_key, "MATCH" if is_match else "UNMATCH"))
                print("\tPOD_ENV_VALUE\t: {0}".format(pod_env_value))
                print("\tAWS_VALUE\t: {0}".format(aws_value))


def main() -> None:
    parser = argparse.ArgumentParser(description="AWS Environment variable checker")
    parser.add_argument("-c", choices=['bcclient', 'bcclient-stream', 'bcmonitoring', 'bctracker-balance', 'bctracker-transaction', 'core', 'relayer'], required=True, help='Component Name')
    parser.add_argument("-yaml", help='k8s multi document yaml')
    parser.add_argument("-doc", help='AWS環境情報取得スクリプトを実行した RAW データファイルのフルパス')
    parser.add_argument("-env", help='podに反映されている環境情報')

    args = parser.parse_args()
    target_component = args.c
    target_k8s_yaml = args.yaml
    target_doc = args.doc
    target_env = args.env

    if "fin" in target_doc:
        target_zone = "fin"
    else:
        target_zone = "biz"

    # Get Target Config
    target_config = get_target_config(target_component, target_zone)

    # Get AWS resource raw data
    aws_resources = get_aws_resource_raw_data(target_doc, target_config)

    # config_definition_check
    if target_k8s_yaml is not None:

        if not os.path.isfile(target_k8s_yaml):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), target_k8s_yaml)

        with open(target_k8s_yaml) as f:
            k8s_multi_docs_yaml = yaml.safe_load_all(f)
            k8s_values_yaml = [k8s_doc for k8s_doc in k8s_multi_docs_yaml]

        # Diff k8s Config Values
        diff_k8s_env_values(k8s_values_yaml, aws_resources)

    # resource_sync_check
    if target_env is not None:
        # Diff Pod Values
        diff_pod_env_values(target_env, aws_resources)


if __name__ == '__main__':
    main()
