envName: "stage-biz-b"

# minReplicas: 3 最小構成のためデフォルトを使用
# maxReplicas: 5 最小構成のためデフォルトを使用

application:
  image:
    name: "************.dkr.ecr.ap-northeast-1.amazonaws.com/stage-biz-b-bcmonitoring"
    tag: "aaaaaa"

newrelic:
  imageName: newrelic/infrastructure-k8s
  imageTag: 2.13.15

serviceAccount:
  roleArn: "arn:aws:iam::************:role/stage-biz-b-tokyo-eks-pod-bcmonitoring"

configMap:
  WEBSOCKET_URI_HOST: "stage-biz-b-tokyo-besu-lb-743da07f7c75175b.elb.ap-northeast-1.amazonaws.com"
  S3_BUCKET_NAME: "stage-biz-b-tokyo-abijson"
  ZONE_ID: "3001"
